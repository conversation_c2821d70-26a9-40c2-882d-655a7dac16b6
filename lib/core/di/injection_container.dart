import 'package:get_it/get_it.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Features
import '../../features/auth/repository/auth_repository.dart';
import '../../features/auth/cubit/auth_cubit.dart';
import '../../features/colleges/repository/colleges_repository.dart';
import '../../features/colleges/cubit/colleges_cubit.dart';
import '../../features/applications/repository/applications_repository.dart';
import '../../features/applications/cubit/applications_cubit.dart';
import '../../features/interviews/repository/interviews_repository.dart';
import '../../features/interviews/cubit/interviews_cubit.dart';
import '../../features/documents/repository/documents_repository.dart';
import '../../features/documents/cubit/documents_cubit.dart';
import '../../features/chat/repository/chat_repository.dart';
import '../../features/chat/cubit/chat_cubit.dart';
import '../../features/profile/repository/profile_repository.dart';
import '../../features/profile/cubit/profile_cubit.dart';
import '../../features/notifications/repository/notifications_repository.dart';
import '../../features/notifications/cubit/notifications_cubit.dart';

final sl = GetIt.instance;

Future<void> init() async {
  // External dependencies
  final sharedPreferences = await SharedPreferences.getInstance();
  sl.registerLazySingleton(() => sharedPreferences);
  
  // Firebase services
  sl.registerLazySingleton(() => FirebaseAuth.instance);
  sl.registerLazySingleton(() => FirebaseFirestore.instance);
  sl.registerLazySingleton(() => FirebaseStorage.instance);
  sl.registerLazySingleton(() => FirebaseMessaging.instance);
  
  // Repositories
  sl.registerLazySingleton<AuthRepository>(
    () => AuthRepository(
      firebaseAuth: sl(),
      firestore: sl(),
      sharedPreferences: sl(),
    ),
  );
  
  sl.registerLazySingleton<CollegesRepository>(
    () => CollegesRepository(
      firestore: sl(),
    ),
  );
  
  sl.registerLazySingleton<ApplicationsRepository>(
    () => ApplicationsRepository(
      firestore: sl(),
    ),
  );
  
  sl.registerLazySingleton<InterviewsRepository>(
    () => InterviewsRepository(
      firestore: sl(),
    ),
  );
  
  sl.registerLazySingleton<DocumentsRepository>(
    () => DocumentsRepository(
      firestore: sl(),
      storage: sl(),
    ),
  );
  
  sl.registerLazySingleton<ChatRepository>(
    () => ChatRepository(
      firestore: sl(),
    ),
  );
  
  sl.registerLazySingleton<ProfileRepository>(
    () => ProfileRepository(
      firestore: sl(),
      storage: sl(),
    ),
  );
  
  sl.registerLazySingleton<NotificationsRepository>(
    () => NotificationsRepository(
      firestore: sl(),
      messaging: sl(),
    ),
  );
  
  // Cubits
  sl.registerFactory(
    () => AuthCubit(
      authRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => CollegesCubit(
      collegesRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => ApplicationsCubit(
      applicationsRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => InterviewsCubit(
      interviewsRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => DocumentsCubit(
      documentsRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => ChatCubit(
      chatRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => ProfileCubit(
      profileRepository: sl(),
    ),
  );
  
  sl.registerFactory(
    () => NotificationsCubit(
      notificationsRepository: sl(),
    ),
  );
}
