import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../repository/notifications_repository.dart';
import '../models/notification_model.dart';

part 'notifications_state.dart';

class NotificationsCubit extends Cubit<NotificationsState> {
  final NotificationsRepository _notificationsRepository;

  NotificationsCubit({required NotificationsRepository notificationsRepository})
    : _notificationsRepository = notificationsRepository,
      super(NotificationsInitial());

  Future<void> loadUserNotifications(String userId) async {
    try {
      emit(NotificationsLoading());
      final notifications = await _notificationsRepository.getUserNotifications(
        userId,
      );
      emit(NotificationsLoaded(notifications: notifications));
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> loadUnreadNotifications(String userId) async {
    try {
      emit(NotificationsLoading());
      final notifications = await _notificationsRepository
          .getUnreadNotifications(userId);
      emit(NotificationsLoaded(notifications: notifications));
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> loadNotificationsByType(String userId, String type) async {
    try {
      emit(NotificationsLoading());
      final notifications = await _notificationsRepository
          .getNotificationsByType(userId, type);
      emit(NotificationsLoaded(notifications: notifications));
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> loadImportantNotifications(String userId) async {
    try {
      emit(NotificationsLoading());
      final notifications = await _notificationsRepository
          .getImportantNotifications(userId);
      emit(NotificationsLoaded(notifications: notifications));
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<int> getUnreadCount(String userId) async {
    try {
      return await _notificationsRepository.getUnreadCount(userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
      return 0;
    }
  }

  Future<void> createNotification(NotificationModel notification) async {
    try {
      await _notificationsRepository.createNotification(notification);
      // Reload notifications after creation
      await loadUserNotifications(notification.userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> markAsRead(String notificationId, String userId) async {
    try {
      await _notificationsRepository.markNotificationAsRead(notificationId);
      // Reload notifications after marking as read
      await loadUserNotifications(userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> markAllAsRead(String userId) async {
    try {
      await _notificationsRepository.markAllNotificationsAsRead(userId);
      // Reload notifications after marking all as read
      await loadUserNotifications(userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> deleteNotification(String notificationId, String userId) async {
    try {
      await _notificationsRepository.deleteNotification(notificationId);
      // Reload notifications after deletion
      await loadUserNotifications(userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> deleteAllNotifications(String userId) async {
    try {
      await _notificationsRepository.deleteAllNotifications(userId);
      // Reload notifications after deletion
      await loadUserNotifications(userId);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<String?> getFCMToken() async {
    try {
      return await _notificationsRepository.getFCMToken();
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
      return null;
    }
  }

  Future<void> saveFCMToken(String userId, String token) async {
    try {
      await _notificationsRepository.saveFCMToken(userId, token);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<bool> requestNotificationPermissions() async {
    try {
      return await _notificationsRepository.requestNotificationPermissions();
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
      return false;
    }
  }

  Future<void> subscribeToTopic(String topic) async {
    try {
      await _notificationsRepository.subscribeToTopic(topic);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }

  Future<void> unsubscribeFromTopic(String topic) async {
    try {
      await _notificationsRepository.unsubscribeFromTopic(topic);
    } catch (e) {
      emit(NotificationsError(message: e.toString()));
    }
  }
}
