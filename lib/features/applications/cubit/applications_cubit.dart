import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../repository/applications_repository.dart';
import '../models/application_model.dart';

part 'applications_state.dart';

class ApplicationsCubit extends Cubit<ApplicationsState> {
  final ApplicationsRepository _applicationsRepository;

  ApplicationsCubit({required ApplicationsRepository applicationsRepository})
    : _applicationsRepository = applicationsRepository,
      super(ApplicationsInitial());

  Future<void> loadUserApplications(String userId) async {
    try {
      emit(ApplicationsLoading());
      final applications = await _applicationsRepository.getUserApplications(
        userId,
      );
      emit(ApplicationsLoaded(applications: applications));
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<void> createApplication(ApplicationModel application) async {
    try {
      emit(ApplicationsLoading());
      await _applicationsRepository.createApplication(application);
      // Reload applications after creation
      await loadUserApplications(application.userId);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<void> updateApplication(
    String id,
    ApplicationModel application,
  ) async {
    try {
      emit(ApplicationsLoading());
      await _applicationsRepository.updateApplication(id, application);
      // Reload applications after update
      await loadUserApplications(application.userId);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<void> updateApplicationStatus(
    String id,
    String status,
    String userId,
  ) async {
    try {
      await _applicationsRepository.updateApplicationStatus(id, status);
      // Reload applications after status update
      await loadUserApplications(userId);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<ApplicationModel?> getApplicationById(String id) async {
    try {
      return await _applicationsRepository.getApplicationById(id);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
      return null;
    }
  }

  Future<void> loadApplicationsByStatus(String userId, String status) async {
    try {
      emit(ApplicationsLoading());
      final applications = await _applicationsRepository
          .getApplicationsByStatus(userId, status);
      emit(ApplicationsLoaded(applications: applications));
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<void> scheduleInterview(
    String applicationId,
    DateTime interviewDate,
    String userId,
  ) async {
    try {
      await _applicationsRepository.scheduleInterview(
        applicationId,
        interviewDate,
      );
      // Reload applications after scheduling interview
      await loadUserApplications(userId);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }

  Future<void> deleteApplication(String id, String userId) async {
    try {
      await _applicationsRepository.deleteApplication(id);
      // Reload applications after deletion
      await loadUserApplications(userId);
    } catch (e) {
      emit(ApplicationsError(message: e.toString()));
    }
  }
}
