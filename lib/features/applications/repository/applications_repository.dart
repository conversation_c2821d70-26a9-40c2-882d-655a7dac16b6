import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';
import '../models/application_model.dart';

class ApplicationsRepository {
  final FirebaseFirestore _firestore;

  ApplicationsRepository({required FirebaseFirestore firestore})
    : _firestore = firestore;

  // Get user applications
  Future<List<ApplicationModel>> getUserApplications(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.applicationsCollection)
          .where('userId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ApplicationModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch user applications: $e');
    }
  }

  // Get application by ID
  Future<ApplicationModel?> getApplicationById(String id) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(id)
          .get();

      if (!doc.exists) return null;

      return ApplicationModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch application: $e');
    }
  }

  // Create application
  Future<String> createApplication(ApplicationModel application) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.applicationsCollection)
          .add(application.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create application: $e');
    }
  }

  // Update application
  Future<void> updateApplication(
    String id,
    ApplicationModel application,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(id)
          .update(application.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update application: $e');
    }
  }

  // Update application status
  Future<void> updateApplicationStatus(String id, String status) async {
    try {
      await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(id)
          .update({
            'status': status,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to update application status: $e');
    }
  }

  // Add document to application
  Future<void> addDocumentToApplication(
    String applicationId,
    String documentId,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(applicationId)
          .update({
            'documentIds': FieldValue.arrayUnion([documentId]),
            'submittedDocuments': FieldValue.arrayUnion([documentId]),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to add document to application: $e');
    }
  }

  // Schedule interview for application
  Future<void> scheduleInterview(
    String applicationId,
    DateTime interviewDate,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(applicationId)
          .update({
            'interviewDate': Timestamp.fromDate(interviewDate),
            'status': AppConstants.statusInterviewScheduled,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to schedule interview: $e');
    }
  }

  // Get applications by college
  Future<List<ApplicationModel>> getApplicationsByCollege(
    String collegeId,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.applicationsCollection)
          .where('collegeId', isEqualTo: collegeId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ApplicationModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch applications by college: $e');
    }
  }

  // Get applications by status
  Future<List<ApplicationModel>> getApplicationsByStatus(
    String userId,
    String status,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.applicationsCollection)
          .where('userId', isEqualTo: userId)
          .where('status', isEqualTo: status)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ApplicationModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch applications by status: $e');
    }
  }

  // Delete application
  Future<void> deleteApplication(String id) async {
    try {
      await _firestore
          .collection(AppConstants.applicationsCollection)
          .doc(id)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete application: $e');
    }
  }
}
