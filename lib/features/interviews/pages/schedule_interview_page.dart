import 'package:flutter/material.dart';
import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';

class ScheduleInterviewPage extends StatelessWidget {
  final String collegeId;
  
  const ScheduleInterviewPage({super.key, required this.collegeId});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Schedule Interview'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.schedule,
              size: 64,
              color: AppColors.primary,
            ),
            const SizedBox(height: 16),
            const Text(
              'Schedule Interview Page',
              style: AppTextStyles.headlineSmall,
            ),
            const SizedBox(height: 8),
            if (collegeId.isNotEmpty) ...[
              Text(
                'College ID: $collegeId',
                style: AppTextStyles.bodyMedium,
              ),
              const SizedBox(height: 8),
            ],
            const Text(
              'This page will be implemented soon',
              style: AppTextStyles.bodyMedium,
            ),
          ],
        ),
      ),
    );
  }
}
