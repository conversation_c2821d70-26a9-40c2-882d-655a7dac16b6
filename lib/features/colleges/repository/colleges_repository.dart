import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';
import '../models/college_model.dart';
import '../../../core/repositories/base_colleges_repository.dart';

class FirebaseCollegesRepository implements BaseCollegesRepository {
  final FirebaseFirestore _firestore;

  FirebaseCollegesRepository({required FirebaseFirestore firestore})
    : _firestore = firestore;

  // Get all colleges
  Future<List<CollegeModel>> getColleges({
    int limit = AppConstants.defaultPageSize,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .limit(limit);

      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      final snapshot = await query.get();
      return snapshot.docs
          .map(
            (doc) => CollegeModel.fromMap({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }),
          )
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges: $e');
    }
  }

  // Get college by ID
  @override
  Future<CollegeModel?> getCollegeById(String id) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.collegesCollection)
          .doc(id)
          .get();

      if (!doc.exists) return null;

      return CollegeModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch college: $e');
    }
  }

  // Search colleges
  @override
  Future<List<CollegeModel>> searchColleges(
    String query, {
    List<String>? programs,
    String? prefecture,
    double? minRating,
    int limit = AppConstants.defaultPageSize,
  }) async {
    try {
      Query firestoreQuery = _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true);

      // Add filters
      if (prefecture != null && prefecture.isNotEmpty) {
        firestoreQuery = firestoreQuery.where(
          'prefecture',
          isEqualTo: prefecture,
        );
      }

      if (minRating != null) {
        firestoreQuery = firestoreQuery.where(
          'rating',
          isGreaterThanOrEqualTo: minRating,
        );
      }

      if (programs != null && programs.isNotEmpty) {
        firestoreQuery = firestoreQuery.where(
          'programsOffered',
          arrayContainsAny: programs,
        );
      }

      firestoreQuery = firestoreQuery.limit(limit);

      final snapshot = await firestoreQuery.get();
      List<CollegeModel> colleges = snapshot.docs
          .map(
            (doc) => CollegeModel.fromMap({
              ...doc.data() as Map<String, dynamic>,
              'id': doc.id,
            }),
          )
          .toList();

      // Filter by name/description if query is provided
      if (query.isNotEmpty) {
        final lowerQuery = query.toLowerCase();
        colleges = colleges.where((college) {
          return college.name.toLowerCase().contains(lowerQuery) ||
              college.description.toLowerCase().contains(lowerQuery) ||
              college.city.toLowerCase().contains(lowerQuery);
        }).toList();
      }

      return colleges;
    } catch (e) {
      throw Exception('Failed to search colleges: $e');
    }
  }

  // Get colleges by prefecture
  @override
  Future<List<CollegeModel>> getCollegesByPrefecture(String prefecture) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('prefecture', isEqualTo: prefecture)
          .where('isActive', isEqualTo: true)
          .orderBy('name')
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch colleges by prefecture: $e');
    }
  }

  // Get featured colleges
  Future<List<CollegeModel>> getFeaturedColleges() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .where('isVerified', isEqualTo: true)
          .orderBy('rating', descending: true)
          .limit(10)
          .get();

      return snapshot.docs
          .map((doc) => CollegeModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch featured colleges: $e');
    }
  }

  // Get available programs
  @override
  Future<List<String>> getAvailablePrograms() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      final Set<String> programs = {};
      for (final doc in snapshot.docs) {
        final college = CollegeModel.fromMap({...doc.data(), 'id': doc.id});
        programs.addAll(college.programsOffered);
      }

      return programs.toList()..sort();
    } catch (e) {
      throw Exception('Failed to fetch available programs: $e');
    }
  }

  // Get available prefectures
  @override
  Future<List<String>> getAvailablePrefectures() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.collegesCollection)
          .where('isActive', isEqualTo: true)
          .get();

      final Set<String> prefectures = {};
      for (final doc in snapshot.docs) {
        final college = CollegeModel.fromMap({...doc.data(), 'id': doc.id});
        prefectures.add(college.prefecture);
      }

      return prefectures.toList()..sort();
    } catch (e) {
      throw Exception('Failed to fetch available prefectures: $e');
    }
  }
}
