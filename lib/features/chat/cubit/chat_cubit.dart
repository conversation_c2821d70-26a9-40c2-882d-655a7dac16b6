import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import '../repository/chat_repository.dart';
import '../models/chat_room_model.dart';
import '../models/message_model.dart';

part 'chat_state.dart';

class ChatCubit extends Cubit<ChatState> {
  final ChatRepository _chatRepository;

  ChatCubit({required ChatRepository chatRepository})
    : _chatRepository = chatRepository,
      super(ChatInitial());

  Future<void> loadUserChatRooms(String userId) async {
    try {
      emit(ChatLoading());
      final chatRooms = await _chatRepository.getUserChatRooms(userId);
      emit(ChatRoomsLoaded(chatRooms: chatRooms));
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> loadChatMessages(String roomId) async {
    try {
      emit(ChatLoading());
      final messages = await _chatRepository.getChatMessages(roomId);
      emit(ChatMessagesLoaded(messages: messages));
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> sendMessage(String roomId, MessageModel message) async {
    try {
      await _chatRepository.sendMessage(roomId, message);
      // Reload messages after sending
      await loadChatMessages(roomId);
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> createChatRoom(ChatRoomModel chatRoom) async {
    try {
      emit(ChatLoading());
      await _chatRepository.createChatRoom(chatRoom);
      // Reload chat rooms after creation
      if (chatRoom.participantIds.isNotEmpty) {
        await loadUserChatRooms(chatRoom.participantIds.first);
      }
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<ChatRoomModel?> getChatRoomById(String roomId) async {
    try {
      return await _chatRepository.getChatRoomById(roomId);
    } catch (e) {
      emit(ChatError(message: e.toString()));
      return null;
    }
  }

  Future<void> markMessagesAsRead(String roomId, String userId) async {
    try {
      await _chatRepository.markMessagesAsRead(roomId, userId);
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> editMessage(
    String roomId,
    String messageId,
    String newContent,
  ) async {
    try {
      await _chatRepository.editMessage(roomId, messageId, newContent);
      // Reload messages after editing
      await loadChatMessages(roomId);
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> deleteMessage(String roomId, String messageId) async {
    try {
      await _chatRepository.deleteMessage(roomId, messageId);
      // Reload messages after deletion
      await loadChatMessages(roomId);
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  Future<void> loadChatRoomsByType(String userId, String type) async {
    try {
      emit(ChatLoading());
      final chatRooms = await _chatRepository.getChatRoomsByType(userId, type);
      emit(ChatRoomsLoaded(chatRooms: chatRooms));
    } catch (e) {
      emit(ChatError(message: e.toString()));
    }
  }

  // Get real-time messages stream
  Stream<List<MessageModel>> getChatMessagesStream(String roomId) {
    return _chatRepository.getChatMessagesStream(roomId);
  }
}
