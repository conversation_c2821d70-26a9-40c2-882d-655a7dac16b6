import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../core/constants/app_constants.dart';
import '../models/chat_room_model.dart';
import '../models/message_model.dart';

class ChatRepository {
  final FirebaseFirestore _firestore;

  ChatRepository({required FirebaseFirestore firestore})
    : _firestore = firestore;

  // Get user chat rooms
  Future<List<ChatRoomModel>> getUserChatRooms(String userId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .where('participantIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageTime', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ChatRoomModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch user chat rooms: $e');
    }
  }

  // Get chat messages
  Future<List<MessageModel>> getChatMessages(
    String roomId, {
    int limit = 50,
  }) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .orderBy('sentAt', descending: true)
          .limit(limit)
          .get();

      return snapshot.docs
          .map((doc) => MessageModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch chat messages: $e');
    }
  }

  // Get real-time chat messages stream
  Stream<List<MessageModel>> getChatMessagesStream(
    String roomId, {
    int limit = 50,
  }) {
    try {
      return _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .orderBy('sentAt', descending: true)
          .limit(limit)
          .snapshots()
          .map(
            (snapshot) => snapshot.docs
                .map(
                  (doc) => MessageModel.fromMap({...doc.data(), 'id': doc.id}),
                )
                .toList(),
          );
    } catch (e) {
      throw Exception('Failed to get chat messages stream: $e');
    }
  }

  // Send message
  Future<String> sendMessage(String roomId, MessageModel message) async {
    try {
      // Add message to subcollection
      final messageRef = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .add(message.toMap());

      // Update chat room with last message info
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .update({
            'lastMessageId': messageRef.id,
            'lastMessage': message.content,
            'lastMessageSenderId': message.senderId,
            'lastMessageSenderName': message.senderName,
            'lastMessageTime': Timestamp.fromDate(message.sentAt),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });

      return messageRef.id;
    } catch (e) {
      throw Exception('Failed to send message: $e');
    }
  }

  // Create chat room
  Future<String> createChatRoom(ChatRoomModel chatRoom) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .add(chatRoom.toMap());

      return docRef.id;
    } catch (e) {
      throw Exception('Failed to create chat room: $e');
    }
  }

  // Get chat room by ID
  Future<ChatRoomModel?> getChatRoomById(String roomId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .get();

      if (!doc.exists) return null;

      return ChatRoomModel.fromMap({...doc.data()!, 'id': doc.id});
    } catch (e) {
      throw Exception('Failed to fetch chat room: $e');
    }
  }

  // Update chat room
  Future<void> updateChatRoom(String roomId, ChatRoomModel chatRoom) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .update(chatRoom.copyWith(updatedAt: DateTime.now()).toMap());
    } catch (e) {
      throw Exception('Failed to update chat room: $e');
    }
  }

  // Add participant to chat room
  Future<void> addParticipant(
    String roomId,
    String userId,
    String userName,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .update({
            'participantIds': FieldValue.arrayUnion([userId]),
            'participantNames': FieldValue.arrayUnion([userName]),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to add participant: $e');
    }
  }

  // Remove participant from chat room
  Future<void> removeParticipant(
    String roomId,
    String userId,
    String userName,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .update({
            'participantIds': FieldValue.arrayRemove([userId]),
            'participantNames': FieldValue.arrayRemove([userName]),
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to remove participant: $e');
    }
  }

  // Mark messages as read
  Future<void> markMessagesAsRead(String roomId, String userId) async {
    try {
      // Update unread count for user
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .update({
            'unreadCounts.$userId': 0,
            'updatedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to mark messages as read: $e');
    }
  }

  // Update message read status
  Future<void> updateMessageReadStatus(
    String roomId,
    String messageId,
    String userId,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .doc(messageId)
          .update({
            'readByUserIds': FieldValue.arrayUnion([userId]),
            'readTimestamps.$userId': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to update message read status: $e');
    }
  }

  // Delete message
  Future<void> deleteMessage(String roomId, String messageId) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .doc(messageId)
          .update({
            'isDeleted': true,
            'deletedAt': Timestamp.fromDate(DateTime.now()),
            'content': '',
          });
    } catch (e) {
      throw Exception('Failed to delete message: $e');
    }
  }

  // Edit message
  Future<void> editMessage(
    String roomId,
    String messageId,
    String newContent,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .doc(roomId)
          .collection(AppConstants.messagesCollection)
          .doc(messageId)
          .update({
            'content': newContent,
            'isEdited': true,
            'editedAt': Timestamp.fromDate(DateTime.now()),
          });
    } catch (e) {
      throw Exception('Failed to edit message: $e');
    }
  }

  // Get chat rooms by type
  Future<List<ChatRoomModel>> getChatRoomsByType(
    String userId,
    String type,
  ) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.chatRoomsCollection)
          .where('participantIds', arrayContains: userId)
          .where('type', isEqualTo: type)
          .where('isActive', isEqualTo: true)
          .orderBy('lastMessageTime', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => ChatRoomModel.fromMap({...doc.data(), 'id': doc.id}))
          .toList();
    } catch (e) {
      throw Exception('Failed to fetch chat rooms by type: $e');
    }
  }
}
