import 'dart:async';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../repository/auth_repository.dart';
import '../models/user_model.dart';

part 'auth_state.dart';

class AuthCubit extends Cubit<AuthState> {
  final AuthRepository _authRepository;
  StreamSubscription<User?>? _authStateSubscription;

  AuthCubit({required AuthRepository authRepository})
      : _authRepository = authRepository,
        super(AuthInitial()) {
    _initializeAuthListener();
  }

  // Initialize auth state listener
  void _initializeAuthListener() {
    _authStateSubscription = _authRepository.authStateChanges.listen(
      (user) async {
        if (user != null) {
          await _handleUserSignedIn(user);
        } else {
          emit(AuthUnauthenticated());
        }
      },
    );
  }

  // Handle user signed in
  Future<void> _handleUserSignedIn(User user) async {
    try {
      emit(AuthLoading());
      final userModel = await _authRepository.getCurrentUserData();
      
      if (userModel != null) {
        emit(AuthAuthenticated(user: userModel));
      } else {
        // User exists in Firebase Auth but not in Firestore
        await _authRepository.signOut();
        emit(AuthError(message: 'User data not found. Please contact support.'));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Check initial auth status
  Future<void> checkAuthStatus() async {
    try {
      emit(AuthLoading());
      
      final user = _authRepository.currentUser;
      if (user != null) {
        await _handleUserSignedIn(user);
      } else {
        emit(AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Register with email and password
  Future<void> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    required String role,
    String? phoneNumber,
  }) async {
    try {
      emit(AuthLoading());
      
      // Check if email is already registered
      final isEmailRegistered = await _authRepository.isEmailRegistered(email);
      if (isEmailRegistered) {
        emit(AuthError(message: 'An account already exists for this email.'));
        return;
      }

      final userModel = await _authRepository.registerWithEmailAndPassword(
        email: email,
        password: password,
        firstName: firstName,
        lastName: lastName,
        role: role,
        phoneNumber: phoneNumber,
      );

      emit(AuthRegistered(user: userModel));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Sign in with email and password
  Future<void> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    try {
      emit(AuthLoading());
      
      final userModel = await _authRepository.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      emit(AuthAuthenticated(user: userModel));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Sign out
  Future<void> signOut() async {
    try {
      emit(AuthLoading());
      await _authRepository.signOut();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Send password reset email
  Future<void> sendPasswordResetEmail(String email) async {
    try {
      emit(AuthLoading());
      await _authRepository.sendPasswordResetEmail(email);
      emit(AuthPasswordResetSent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Send email verification
  Future<void> sendEmailVerification() async {
    try {
      await _authRepository.sendEmailVerification();
      emit(AuthEmailVerificationSent());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Check email verification status
  Future<void> checkEmailVerification() async {
    try {
      emit(AuthLoading());
      await _authRepository.reloadUser();
      
      final user = _authRepository.currentUser;
      if (user != null && user.emailVerified) {
        // Update user data in Firestore
        final userModel = await _authRepository.getCurrentUserData();
        if (userModel != null) {
          final updatedUser = userModel.copyWith(isEmailVerified: true);
          await _authRepository.updateUserData(updatedUser);
          emit(AuthAuthenticated(user: updatedUser));
        }
      } else {
        final userModel = await _authRepository.getCurrentUserData();
        if (userModel != null) {
          emit(AuthAuthenticated(user: userModel));
        }
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Update user profile
  Future<void> updateUserProfile(UserModel updatedUser) async {
    try {
      emit(AuthLoading());
      await _authRepository.updateUserData(updatedUser);
      emit(AuthAuthenticated(user: updatedUser));
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Update password
  Future<void> updatePassword(String newPassword) async {
    try {
      emit(AuthLoading());
      await _authRepository.updatePassword(newPassword);
      
      // Get current user data
      final userModel = await _authRepository.getCurrentUserData();
      if (userModel != null) {
        emit(AuthAuthenticated(user: userModel));
      }
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Delete account
  Future<void> deleteAccount() async {
    try {
      emit(AuthLoading());
      await _authRepository.deleteAccount();
      emit(AuthUnauthenticated());
    } catch (e) {
      emit(AuthError(message: e.toString()));
    }
  }

  // Clear error state
  void clearError() {
    if (state is AuthError) {
      emit(AuthUnauthenticated());
    }
  }

  // Get current user
  UserModel? get currentUser {
    final state = this.state;
    if (state is AuthAuthenticated) {
      return state.user;
    }
    return null;
  }

  // Check if user is authenticated
  bool get isAuthenticated => state is AuthAuthenticated;

  // Check if user is loading
  bool get isLoading => state is AuthLoading;

  @override
  Future<void> close() {
    _authStateSubscription?.cancel();
    return super.close();
  }
}
